// Complete Prisma schema file based on Laravel migration v1.6.0
// This schema includes all models from the e-school system

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// Core Models
model School {
  id           Int       @id @default(autoincrement())
  name         String
  address      String
  supportPhone String    @map("support_phone")
  supportEmail String    @map("support_email")
  tagline      String
  logo         String
  adminId      Int?      @map("admin_id")
  status       Int       @default(0)
  domain       String?
  databaseName String?   @map("database_name")
  code         String?
  type         String?   @default("custom")
  domainType   String?   @default("default") @map("domain_type")
  createdAt    DateTime  @default(now()) @map("created_at")
  updatedAt    DateTime  @updatedAt @map("updated_at")
  deletedAt    DateTime? @map("deleted_at")

  @@map("schools")
}

model User {
  id                 Int       @id @default(autoincrement())
  firstName          String    @map("first_name")
  lastName           String    @map("last_name")
  email              String    @unique
  emailVerifiedAt    DateTime? @map("email_verified_at")
  twoFactorEnabled   Int       @default(1) @map("two_factor_enabled")
  twoFactorSecret    String?   @map("two_factor_secret")
  twoFactorExpiresAt String?   @map("two_factor_expires_at")
  password           String
  image              String?
  dob                DateTime?
  gender             String?
  mobile             String?
  currentAddress     String?   @map("current_address")
  permanentAddress   String?   @map("permanent_address")
  occupation         String?
  status             Int       @default(1)
  resetPasswordToken String?   @map("reset_password_token")
  fcmId              String?   @map("fcm_id")
  schoolId           Int?      @map("school_id")
  language           String    @default("en")
  rememberToken      String?   @map("remember_token")
  createdAt          DateTime  @default(now()) @map("created_at")
  updatedAt          DateTime  @updatedAt @map("updated_at")
  deletedAt          DateTime? @map("deleted_at")

  @@map("users")
}

model Role {
  id         Int      @id @default(autoincrement())
  name       String
  guardName  String   @map("guard_name")
  schoolId   Int?     @map("school_id")
  customRole Boolean  @default(true) @map("custom_role")
  editable   Boolean  @default(true)
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")

  @@unique([name, guardName, schoolId])
  @@map("roles")
}

// Academic Structure Models
model Category {
  id        Int       @id @default(autoincrement())
  name      String    @db.VarChar(512)
  schoolId  Int       @map("school_id")
  status    Int       @default(1)
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  @@map("categories")
}

model Section {
  id        Int       @id @default(autoincrement())
  name      String    @db.VarChar(512)
  schoolId  Int       @map("school_id")
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  @@map("sections")
}

model Medium {
  id        Int       @id @default(autoincrement())
  name      String    @db.VarChar(512)
  schoolId  Int       @map("school_id")
  deletedAt DateTime? @map("deleted_at")
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")

  @@map("mediums")
}

model SessionYear {
  id        Int       @id @default(autoincrement())
  name      String    @db.VarChar(512)
  default   Int       @default(0)
  startDate DateTime  @map("start_date") @db.Date
  endDate   DateTime  @map("end_date") @db.Date
  schoolId  Int       @map("school_id")
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  @@unique([name, schoolId])
  @@map("session_years")
}

model Semester {
  id         Int       @id @default(autoincrement())
  name       String
  startMonth Int       @map("start_month")
  endMonth   Int       @map("end_month")
  schoolId   Int       @map("school_id")
  createdAt  DateTime  @default(now()) @map("created_at")
  updatedAt  DateTime  @updatedAt @map("updated_at")
  deletedAt  DateTime? @map("deleted_at")

  @@map("semesters")
}

model Language {
  id        Int      @id @default(autoincrement())
  name      String   @db.VarChar(512)
  code      String   @unique @db.VarChar(64)
  file      String   @db.VarChar(512)
  status    Int      @default(0)
  isRtl     Int      @default(0) @map("is_rtl")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("languages")
}

model Slider {
  id        Int      @id @default(autoincrement())
  image     String   @db.VarChar(1024)
  link      String?
  type      Int      @default(1)
  schoolId  Int      @map("school_id")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("sliders")
}

model Subject {
  id        Int       @id @default(autoincrement())
  name      String    @db.VarChar(512)
  code      String?   @db.VarChar(64)
  bgColor   String    @map("bg_color") @db.VarChar(32)
  image     String    @db.VarChar(512)
  mediumId  Int       @map("medium_id")
  type      String    @db.VarChar(64)
  schoolId  Int       @map("school_id")
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  @@map("subjects")
}

model Stream {
  id        Int       @id @default(autoincrement())
  name      String
  schoolId  Int       @map("school_id")
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  @@map("streams")
}

model Shift {
  id        Int       @id @default(autoincrement())
  name      String
  startTime DateTime  @map("start_time") @db.Time(0)
  endTime   DateTime  @map("end_time") @db.Time(0)
  status    Int       @default(1)
  schoolId  Int       @map("school_id")
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  @@map("shifts")
}

model Class {
  id               Int       @id @default(autoincrement())
  name             String    @db.VarChar(512)
  includeSemesters Int       @default(0) @map("include_semesters")
  mediumId         Int       @map("medium_id")
  shiftId          Int?      @map("shift_id")
  streamId         Int?      @map("stream_id")
  schoolId         Int       @map("school_id")
  createdAt        DateTime  @default(now()) @map("created_at")
  updatedAt        DateTime  @updatedAt @map("updated_at")
  deletedAt        DateTime? @map("deleted_at")

  @@map("classes")
}

model ElectiveSubjectGroup {
  id                      Int       @id @default(autoincrement())
  totalSubjects           Int       @map("total_subjects")
  totalSelectableSubjects Int       @map("total_selectable_subjects")
  classId                 Int       @map("class_id")
  semesterId              Int?      @map("semester_id")
  schoolId                Int       @map("school_id")
  createdAt               DateTime  @default(now()) @map("created_at")
  updatedAt               DateTime  @updatedAt @map("updated_at")
  deletedAt               DateTime? @map("deleted_at")

  @@map("elective_subject_groups")
}

model ClassSubject {
  id                     Int       @id @default(autoincrement())
  classId                Int       @map("class_id")
  subjectId              Int       @map("subject_id")
  type                   String    @db.VarChar(32)
  electiveSubjectGroupId Int?      @map("elective_subject_group_id")
  semesterId             Int?      @map("semester_id")
  virtualSemesterId      Int       @map("virtual_semester_id")
  schoolId               Int       @map("school_id")
  deletedAt              DateTime? @map("deleted_at")
  createdAt              DateTime  @default(now()) @map("created_at")
  updatedAt              DateTime  @updatedAt @map("updated_at")

  @@unique([classId, subjectId, virtualSemesterId], name: "unique_ids")
  @@map("class_subjects")
}

model Staff {
  id            Int       @id @default(autoincrement())
  userId        Int       @unique @map("user_id")
  qualification String?   @db.VarChar(512)
  salary        Float     @default(0)
  joiningDate   DateTime? @map("joining_date") @db.Date
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")

  @@map("staffs")
}

model ClassSection {
  id        Int       @id @default(autoincrement())
  classId   Int       @map("class_id")
  sectionId Int       @map("section_id")
  mediumId  Int       @map("medium_id")
  schoolId  Int       @map("school_id")
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  @@unique([classId, sectionId, mediumId], name: "unique_id")
  @@map("class_sections")
}

model Student {
  id                Int       @id @default(autoincrement())
  userId            Int       @map("user_id")
  classId           Int?      @map("class_id")
  classSectionId    Int?      @map("class_section_id")
  applicationType   String?   @default("offline") @map("application_type")
  admissionNo       String    @map("admission_no") @db.VarChar(512)
  rollNumber        Int?      @map("roll_number")
  admissionDate     DateTime  @map("admission_date") @db.Date
  schoolId          Int       @map("school_id")
  applicationStatus Int?      @default(1) @map("application_status")
  guardianId        Int       @map("guardian_id")
  sessionYearId     Int       @map("session_year_id")
  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @updatedAt @map("updated_at")
  deletedAt         DateTime? @map("deleted_at")

  @@map("students")
}

// Fees Management Models
model Fee {
  id               Int       @id @default(autoincrement())
  name             String
  dueDate          DateTime  @map("due_date") @db.Date
  dueCharges       Float     @map("due_charges")
  dueChargesAmount Float     @map("due_charges_amount")
  classId          Int       @map("class_id")
  schoolId         Int       @map("school_id")
  sessionYearId    Int       @map("session_year_id")
  createdAt        DateTime  @default(now()) @map("created_at")
  updatedAt        DateTime  @updatedAt @map("updated_at")
  deletedAt        DateTime? @map("deleted_at")

  @@map("fees")
}

model FeesInstallment {
  id             Int             @id @default(autoincrement())
  name           String
  dueDate        DateTime        @map("due_date") @db.Date
  dueChargesType FeesChargesType @map("due_charges_type")
  dueCharges     Int             @map("due_charges")
  feesId         Int             @map("fees_id")
  sessionYearId  Int             @map("session_year_id")
  schoolId       Int             @map("school_id")
  createdAt      DateTime        @default(now()) @map("created_at")
  updatedAt      DateTime        @updatedAt @map("updated_at")

  @@map("fees_installments")
}

model FeesType {
  id          Int       @id @default(autoincrement())
  name        String
  description String?
  schoolId    Int       @map("school_id")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")
  deletedAt   DateTime? @map("deleted_at")

  @@map("fees_types")
}

model FeesClassType {
  id         Int      @id @default(autoincrement())
  classId    Int      @map("class_id")
  feesId     Int      @map("fees_id")
  feesTypeId Int      @map("fees_type_id")
  amount     Decimal  @db.Decimal(64, 2)
  optional   Boolean
  schoolId   Int      @map("school_id")
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")

  @@unique([classId, feesTypeId, schoolId, feesId], name: "unique_ids")
  @@map("fees_class_types")
}

model PaymentTransaction {
  id               Int           @id @default(autoincrement())
  userId           Int           @map("user_id")
  amount           Decimal       @db.Decimal(64, 2)
  paymentGateway   String        @map("payment_gateway") @db.VarChar(128)
  orderId          String?       @map("order_id")
  paymentId        String?       @map("payment_id")
  paymentSignature String?       @map("payment_signature")
  paymentStatus    PaymentStatus @map("payment_status")
  schoolId         Int?          @map("school_id")
  createdAt        DateTime      @default(now()) @map("created_at")
  updatedAt        DateTime      @updatedAt @map("updated_at")

  @@map("payment_transactions")
}

model FeesPaid {
  id                Int       @id @default(autoincrement())
  feesId            Int       @map("fees_id")
  studentId         Int       @map("student_id")
  isFullyPaid       Boolean   @map("is_fully_paid")
  isUsedInstallment Boolean   @map("is_used_installment")
  amount            Decimal   @db.Decimal(8, 2)
  date              DateTime  @db.Date
  schoolId          Int       @map("school_id")
  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @updatedAt @map("updated_at")
  deletedAt         DateTime? @map("deleted_at")

  @@unique([studentId, feesId, schoolId], name: "unique_ids")
  @@map("fees_paids")
}

model CompulsoryFee {
  id                   Int               @id @default(autoincrement())
  studentId            Int               @map("student_id")
  paymentTransactionId Int?              @map("payment_transaction_id")
  type                 CompulsoryFeeType
  installmentId        Int?              @map("installment_id")
  mode                 PaymentMode
  chequeNo             String?           @map("cheque_no")
  amount               Decimal           @db.Decimal(8, 2)
  dueCharges           Decimal?          @map("due_charges") @db.Decimal(8, 2)
  feesPaidId           Int?              @map("fees_paid_id")
  status               PaymentStatusEnum
  date                 DateTime          @db.Date
  schoolId             Int               @map("school_id")
  createdAt            DateTime          @default(now()) @map("created_at")
  updatedAt            DateTime          @updatedAt @map("updated_at")
  deletedAt            DateTime?         @map("deleted_at")

  @@map("compulsory_fees")
}

model OptionalFee {
  id                   Int               @id @default(autoincrement())
  studentId            Int               @map("student_id")
  classId              Int               @map("class_id")
  paymentTransactionId Int?              @map("payment_transaction_id")
  feesClassId          Int?              @map("fees_class_id")
  mode                 PaymentMode
  chequeNo             String?           @map("cheque_no")
  amount               Decimal           @db.Decimal(8, 2)
  feesPaidId           Int?              @map("fees_paid_id")
  date                 DateTime          @db.Date
  schoolId             Int               @map("school_id")
  status               PaymentStatusEnum
  createdAt            DateTime          @default(now()) @map("created_at")
  updatedAt            DateTime          @updatedAt @map("updated_at")
  deletedAt            DateTime?         @map("deleted_at")

  @@map("optional_fees")
}

model FeesAdvance {
  id              Int      @id @default(autoincrement())
  compulsoryFeeId Int      @map("compulsory_fee_id")
  studentId       Int      @map("student_id")
  parentId        Int      @map("parent_id")
  amount          Float
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @updatedAt @map("updated_at")

  @@map("fees_advance")
}

// Package and Subscription Models
model Package {
  id            Int       @id @default(autoincrement())
  name          String?
  description   String?
  tagline       String?
  studentCharge Float     @map("student_charge") @db.Float
  staffCharge   Float     @map("staff_charge") @db.Float
  days          Int       @default(1)
  type          Int       @default(1)
  noOfStudents  Int       @default(0) @map("no_of_students")
  noOfStaffs    Int       @default(0) @map("no_of_staffs")
  charges       Float     @db.Float
  status        Int       @default(0)
  isTrial       Int       @default(0) @map("is_trial")
  highlight     Int       @default(0)
  rank          Int       @default(0)
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  deletedAt     DateTime? @map("deleted_at")

  @@map("packages")
}

model Feature {
  id          Int      @id @default(autoincrement())
  name        String
  isDefault   Int      @default(0) @map("is_default")
  status      Int      @default(1)
  requiredVps Int      @default(0) @map("required_vps")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@map("features")
}

model Subscription {
  id            Int      @id @default(autoincrement())
  schoolId      Int      @map("school_id")
  packageId     Int      @map("package_id")
  name          String
  studentCharge Float    @map("student_charge")
  staffCharge   Float    @map("staff_charge")
  startDate     DateTime @map("start_date") @db.Date
  endDate       DateTime @map("end_date") @db.Date
  billingCycle  Int      @default(0) @map("billing_cycle")
  packageType   Int      @default(1) @map("package_type")
  noOfStudents  Int      @default(0) @map("no_of_students")
  noOfStaffs    Int      @default(0) @map("no_of_staffs")
  charges       Decimal  @default(0) @db.Decimal(64, 4)
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  @@map("subscriptions")
}

// Enums
enum FeesChargesType {
  fixed
  percentage
}

enum PaymentStatus {
  failed
  succeed
  pending
}

enum CompulsoryFeeType {
  Full_Payment        @map("Full Payment")
  Installment_Payment @map("Installment Payment")
}

enum PaymentMode {
  Cash
  Cheque
  Online
}

enum PaymentStatusEnum {
  Success
  Pending
  Failed
}

// Additional Essential Models
model FormField {
  id            Int       @id @default(autoincrement())
  name          String    @db.VarChar(128)
  type          String    @db.VarChar(128)
  isRequired    Boolean   @default(false) @map("is_required")
  defaultValues String?   @map("default_values") @db.Text
  schoolId      Int?      @map("school_id")
  rank          Int       @default(0)
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  deletedAt     DateTime? @map("deleted_at")

  @@unique([name, schoolId], name: "name")
  @@map("form_fields")
}

model SchoolSetting {
  id       Int     @id @default(autoincrement())
  name     String
  data     String  @db.Text
  type     String?
  schoolId Int     @map("school_id")

  @@unique([name, schoolId])
  @@map("school_settings")
}

model SystemSetting {
  id   Int     @id @default(autoincrement())
  name String  @unique
  data String  @db.Text
  type String?

  @@map("system_settings")
}

model Holiday {
  id          Int      @id @default(autoincrement())
  date        DateTime @db.Date
  title       String   @db.VarChar(128)
  description String?  @db.LongText
  schoolId    Int      @map("school_id")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@map("holidays")
}

model File {
  id            Int       @id @default(autoincrement())
  modalType     String    @map("modal_type")
  modalId       Int       @map("modal_id")
  fileName      String?   @map("file_name") @db.VarChar(1024)
  fileThumbnail String?   @map("file_thumbnail") @db.VarChar(1024)
  type          String    @db.TinyText
  fileUrl       String    @map("file_url") @db.VarChar(1024)
  schoolId      Int       @map("school_id")
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  deletedAt     DateTime? @map("deleted_at")

  @@map("files")
}

model Attendance {
  id             Int      @id @default(autoincrement())
  classSectionId Int      @map("class_section_id")
  studentId      Int      @map("student_id")
  sessionYearId  Int      @map("session_year_id")
  type           Int
  date           DateTime @db.Date
  remark         String   @db.VarChar(512)
  schoolId       Int      @map("school_id")
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  @@map("attendances")
}

model ExpenseCategory {
  id        Int       @id @default(autoincrement())
  name      String
  schoolId  Int       @map("school_id")
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  @@map("expense_categories")
}

model Expense {
  id            Int      @id @default(autoincrement())
  categoryId    Int?     @map("category_id")
  refNo         String?  @map("ref_no")
  staffId       Int?     @map("staff_id")
  basicSalary   Int      @default(0) @map("basic_salary")
  paidLeaves    Float    @default(0) @map("paid_leaves")
  month         Int?
  year          Int?
  title         String   @db.VarChar(512)
  description   String?
  amount        Decimal  @db.Decimal(64, 2)
  date          DateTime @db.Date
  schoolId      Int      @map("school_id")
  sessionYearId Int      @map("session_year_id")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  @@unique([staffId, month, year], name: "salary_unique_records")
  @@map("expenses")
}

model PaymentConfiguration {
  id               Int      @id @default(autoincrement())
  paymentMethod    String   @map("payment_method")
  apiKey           String   @map("api_key")
  secretKey        String   @map("secret_key")
  webhookSecretKey String   @map("webhook_secret_key")
  currencyCode     String?  @map("currency_code") @db.VarChar(128)
  status           Boolean  @default(true)
  bankName         String?  @map("bank_name")
  accountName      String?  @map("account_name")
  accountNo        String?  @map("account_no")
  schoolId         Int?     @map("school_id")
  createdAt        DateTime @default(now()) @map("created_at")
  updatedAt        DateTime @updatedAt @map("updated_at")

  @@map("payment_configurations")
}

model LeaveMaster {
  id            Int      @id @default(autoincrement())
  leaves        Float
  holiday       String
  sessionYearId Int      @map("session_year_id")
  schoolId      Int      @map("school_id")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  @@map("leave_masters")
}

model Leave {
  id            Int      @id @default(autoincrement())
  userId        Int      @map("user_id")
  reason        String
  fromDate      DateTime @map("from_date") @db.Date
  toDate        DateTime @map("to_date") @db.Date
  status        Int      @default(0)
  schoolId      Int      @map("school_id")
  leaveMasterId Int      @map("leave_master_id")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  @@map("leaves")
}

model Faq {
  id          Int      @id @default(autoincrement())
  title       String
  description String   @db.Text
  schoolId    Int?     @map("school_id")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  @@map("faqs")
}

// Version 1.2.0+ Models
model Gallery {
  id            Int      @id @default(autoincrement())
  title         String
  description   String?
  thumbnail     String?
  sessionYearId Int      @map("session_year_id")
  schoolId      Int      @map("school_id")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  @@map("galleries")
}

model Notification {
  id            Int      @id @default(autoincrement())
  title         String
  message       String?
  image         String?
  sendTo        String   @map("send_to")
  sessionYearId Int      @map("session_year_id")
  schoolId      Int      @map("school_id")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  @@map("notifications")
}

// Version 1.4.0+ Models - Chat System
model Chat {
  id         Int      @id @default(autoincrement())
  senderId   Int      @map("sender_id")
  receiverId Int      @map("receiver_id")
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")

  @@map("chats")
}

model Message {
  id        Int       @id @default(autoincrement())
  chatId    Int       @map("chat_id")
  senderId  Int       @map("sender_id")
  message   String?   @db.Text
  readAt    DateTime? @map("read_at")
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")

  @@map("messages")
}

// Version 1.5.0+ Models
model SchoolInquiry {
  id            Int      @id @default(autoincrement())
  schoolName    String   @map("school_name")
  schoolAddress String   @map("school_address")
  schoolPhone   String   @map("school_phone")
  schoolEmail   String   @map("school_email")
  schoolTagline String   @map("school_tagline")
  date          DateTime @db.Date
  status        Int      @default(0)
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  @@map("school_inquiries")
}

// Version 1.6.0+ Models
model ContactInquiry {
  id        Int       @id @default(autoincrement())
  name      String
  email     String
  message   String    @db.Text
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  @@map("contact_inquiry")
}
