// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model School {
  id           Int       @id @default(autoincrement())
  name         String
  address      String
  supportPhone String    @map("support_phone")
  supportEmail String    @map("support_email")
  tagline      String
  logo         String
  adminId      Int?      @map("admin_id")
  status       Int       @default(0) // 0 => Deactivate, 1 => Active
  domain       String?
  databaseName String?   @map("database_name")
  code         String?
  type         String?   @default("custom")
  domainType   String?   @default("default") @map("domain_type")
  createdAt    DateTime  @default(now()) @map("created_at")
  updatedAt    DateTime  @updatedAt @map("updated_at")
  deletedAt    DateTime? @map("deleted_at")

  // Relations
  admin                     User?                      @relation("SchoolAdmin", fields: [adminId], references: [id])
  users                     User[]                     @relation("SchoolUsers")
  roles                     Role[]
  categories                Category[]
  sections                  Section[]
  mediums                   Medium[]
  sessionYears              SessionYear[]
  semesters                 Semester[]
  sliders                   Slider[]
  subjects                  Subject[]
  streams                   Stream[]
  shifts                    Shift[]
  classes                   Class[]
  electiveSubjectGroups     ElectiveSubjectGroup[]
  classSubjects             ClassSubject[]
  classSections             ClassSection[]
  students                  Student[]
  studentSubjects           StudentSubject[]
  subjectTeachers           SubjectTeacher[]
  lessons                   Lesson[]
  lessonTopics              LessonTopic[]
  assignments               Assignment[]
  assignmentSubmissions     AssignmentSubmission[]
  exams                     Exam[]
  examTimetables            ExamTimetable[]
  examMarks                 ExamMark[]
  examResults               ExamResult[]
  grades                    Grade[]
  timetables                Timetable[]
  announcements             Announcement[]
  academicCalendars         AcademicCalendar[]
  attendances               Attendance[]
  files                     File[]
  holidays                  Holiday[]
  promoteStudents           PromoteStudent[]
  onlineExams               OnlineExam[]
  onlineExamQuestions       OnlineExamQuestion[]
  onlineExamQuestionOptions OnlineExamQuestionOption[]
  onlineExamQuestionChoices OnlineExamQuestionChoice[]
  studentOnlineExamStatuses StudentOnlineExamStatus[]
  onlineExamStudentAnswers  OnlineExamStudentAnswer[]
  formFields                FormField[]
  extraStudentDatas         ExtraStudentData[]
  schoolSettings            SchoolSetting[]
  classTeachers             ClassTeacher[]
  packages                  Package[]
  announcementClasses       AnnouncementClass[]
  features                  Feature[]
  packageFeatures           PackageFeature[]
  fees                      Fee[]
  feesInstallments          FeesInstallment[]
  feesTypes                 FeesType[]
  feesClassTypes            FeesClassType[]
  paymentTransactions       PaymentTransaction[]
  feesPaids                 FeesPaid[]
  compulsoryFees            CompulsoryFee[]
  optionalFees              OptionalFee[]
  feesAdvance               FeesAdvance[]
  subscriptions             Subscription[]
  subscriptionFeatures      SubscriptionFeature[]
  addons                    Addon[]
  addonSubscriptions        AddonSubscription[]
  subscriptionBills         SubscriptionBill[]
  userStatusForNextCycles   UserStatusForNextCycle[]
  expenseCategories         ExpenseCategory[]
  expenses                  Expense[]
  payrollSettings           PayrollSetting[]
  staffPayrolls             StaffPayroll[]
  paymentConfigurations     PaymentConfiguration[]
  leaveMasters              LeaveMaster[]
  leaves                    Leave[]
  leaveDetails              LeaveDetail[]
  staffSupportSchools       StaffSupportSchool[]
  faqs                      Faq[]
  galleries                 Gallery[]
  notifications             Notification[]
  certificateTemplates      CertificateTemplate[]
  classGroups               ClassGroup[]
  databaseBackups           DatabaseBackup[]
  schoolInquiries           SchoolInquiry[]
  extraSchoolDatas          ExtraSchoolData[]

  @@map("schools")
}

model User {
  id                 Int       @id @default(autoincrement())
  firstName          String    @map("first_name")
  lastName           String    @map("last_name")
  email              String    @unique
  emailVerifiedAt    DateTime? @map("email_verified_at")
  twoFactorEnabled   Int       @default(1) @map("two_factor_enabled")
  twoFactorSecret    String?   @map("two_factor_secret")
  twoFactorExpiresAt String?   @map("two_factor_expires_at")
  password           String
  image              String?
  dob                DateTime?
  gender             String?
  mobile             String?
  currentAddress     String?   @map("current_address")
  permanentAddress   String?   @map("permanent_address")
  occupation         String?
  status             Int       @default(1)
  resetPasswordToken String?   @map("reset_password_token")
  fcmId              String?   @map("fcm_id")
  schoolId           Int?      @map("school_id")
  language           String    @default("en")
  rememberToken      String?   @map("remember_token")
  createdAt          DateTime  @default(now()) @map("created_at")
  updatedAt          DateTime  @updatedAt @map("updated_at")
  deletedAt          DateTime? @map("deleted_at")

  // Relations
  school                       School?                   @relation("SchoolUsers", fields: [schoolId], references: [id])
  adminSchools                 School[]                  @relation("SchoolAdmin")
  staff                        Staff?
  students                     Student[]                 @relation("StudentUser")
  guardianStudents             Student[]                 @relation("GuardianUser")
  studentSubjects              StudentSubject[]
  subjectTeachers              SubjectTeacher[]
  assignmentCreated            Assignment[]              @relation("AssignmentCreatedBy")
  assignmentEdited             Assignment[]              @relation("AssignmentEditedBy")
  assignmentSubmissions        AssignmentSubmission[]
  examMarks                    ExamMark[]
  examResults                  ExamResult[]
  attendances                  Attendance[]
  promoteStudents              PromoteStudent[]
  onlineExamQuestionLastEdited OnlineExamQuestion[]
  studentOnlineExamStatuses    StudentOnlineExamStatus[]
  onlineExamStudentAnswers     OnlineExamStudentAnswer[]
  extraStudentDatas            ExtraStudentData[]
  classTeachers                ClassTeacher[]
  paymentTransactions          PaymentTransaction[]
  feesPaids                    FeesPaid[]
  compulsoryFees               CompulsoryFee[]
  optionalFees                 OptionalFee[]
  feesAdvanceStudent           FeesAdvance[]             @relation("FeesAdvanceStudent")
  feesAdvanceParent            FeesAdvance[]             @relation("FeesAdvanceParent")
  leaves                       Leave[]
  staffSupportSchools          StaffSupportSchool[]
  sentChats                    Chat[]                    @relation("ChatSender")
  receivedChats                Chat[]                    @relation("ChatReceiver")
  sentMessages                 Message[]
  userStatusForNextCycles      UserStatusForNextCycle[]

  @@map("users")
}

model Role {
  id         Int      @id @default(autoincrement())
  name       String
  guardName  String   @map("guard_name")
  schoolId   Int?     @map("school_id")
  customRole Boolean  @default(true) @map("custom_role")
  editable   Boolean  @default(true)
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")

  // Relations
  school School? @relation(fields: [schoolId], references: [id])

  @@unique([name, guardName, schoolId])
  @@map("roles")
}

model Category {
  id        Int       @id @default(autoincrement())
  name      String    @db.VarChar(512)
  schoolId  Int       @map("school_id")
  status    Int       @default(1)
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  // Relations
  school School @relation(fields: [schoolId], references: [id])

  @@map("categories")
}

model Section {
  id        Int       @id @default(autoincrement())
  name      String    @db.VarChar(512)
  schoolId  Int       @map("school_id")
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  // Relations
  school        School         @relation(fields: [schoolId], references: [id])
  classSections ClassSection[]

  @@map("sections")
}

model Medium {
  id        Int       @id @default(autoincrement())
  name      String    @db.VarChar(512)
  schoolId  Int       @map("school_id")
  deletedAt DateTime? @map("deleted_at")
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")

  // Relations
  school        School         @relation(fields: [schoolId], references: [id])
  subjects      Subject[]
  classes       Class[]
  classSections ClassSection[]

  @@map("mediums")
}

model SessionYear {
  id        Int       @id @default(autoincrement())
  name      String    @db.VarChar(512)
  default   Int       @default(0)
  startDate DateTime  @map("start_date") @db.Date
  endDate   DateTime  @map("end_date") @db.Date
  schoolId  Int       @map("school_id")
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  // Relations
  school                School                 @relation(fields: [schoolId], references: [id])
  students              Student[]
  assignments           Assignment[]
  assignmentSubmissions AssignmentSubmission[]
  exams                 Exam[]
  examTimetables        ExamTimetable[]
  examMarks             ExamMark[]
  examResults           ExamResult[]
  attendances           Attendance[]
  promoteStudents       PromoteStudent[]
  onlineExams           OnlineExam[]
  academicCalendars     AcademicCalendar[]
  announcements         Announcement[]
  fees                  Fee[]
  feesInstallments      FeesInstallment[]
  expenses              Expense[]
  leaveMasters          LeaveMaster[]
  galleries             Gallery[]
  notifications         Notification[]

  @@unique([name, schoolId])
  @@map("session_years")
}

model Semester {
  id         Int       @id @default(autoincrement())
  name       String
  startMonth Int       @map("start_month")
  endMonth   Int       @map("end_month")
  schoolId   Int       @map("school_id")
  createdAt  DateTime  @default(now()) @map("created_at")
  updatedAt  DateTime  @updatedAt @map("updated_at")
  deletedAt  DateTime? @map("deleted_at")

  // Relations
  school                School                 @relation(fields: [schoolId], references: [id])
  electiveSubjectGroups ElectiveSubjectGroup[]
  classSubjects         ClassSubject[]
  timetables            Timetable[]

  @@map("semesters")
}

model Language {
  id        Int      @id @default(autoincrement())
  name      String   @db.VarChar(512)
  code      String   @unique @db.VarChar(64)
  file      String   @db.VarChar(512)
  status    Int      @default(0) // 1=>active
  isRtl     Int      @default(0) @map("is_rtl")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("languages")
}

model Slider {
  id        Int      @id @default(autoincrement())
  image     String   @db.VarChar(1024)
  link      String?
  type      Int      @default(1) // 1 => App, 2 => web, 3 => Both
  schoolId  Int      @map("school_id")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  school School @relation(fields: [schoolId], references: [id])

  @@map("sliders")
}

model Subject {
  id        Int       @id @default(autoincrement())
  name      String    @db.VarChar(512)
  code      String?   @db.VarChar(64)
  bgColor   String    @map("bg_color") @db.VarChar(32)
  image     String    @db.VarChar(512)
  mediumId  Int       @map("medium_id")
  type      String    @db.VarChar(64) // Theory / Practical
  schoolId  Int       @map("school_id")
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  // Relations
  school          School           @relation(fields: [schoolId], references: [id])
  medium          Medium           @relation(fields: [mediumId], references: [id])
  classSubjects   ClassSubject[]
  subjectTeachers SubjectTeacher[]
  timetables      Timetable[]

  @@map("subjects")
}

model Stream {
  id        Int       @id @default(autoincrement())
  name      String
  schoolId  Int       @map("school_id")
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  // Relations
  school  School  @relation(fields: [schoolId], references: [id])
  classes Class[]

  @@map("streams")
}

model Shift {
  id        Int       @id @default(autoincrement())
  name      String
  startTime DateTime  @map("start_time") @db.Time(0)
  endTime   DateTime  @map("end_time") @db.Time(0)
  status    Int       @default(1)
  schoolId  Int       @map("school_id")
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  // Relations
  school  School  @relation(fields: [schoolId], references: [id])
  classes Class[]

  @@map("shifts")
}

model Class {
  id               Int       @id @default(autoincrement())
  name             String    @db.VarChar(512)
  includeSemesters Int       @default(0) @map("include_semesters") // 0 - no 1 - yes
  mediumId         Int       @map("medium_id")
  shiftId          Int?      @map("shift_id")
  streamId         Int?      @map("stream_id")
  schoolId         Int       @map("school_id")
  createdAt        DateTime  @default(now()) @map("created_at")
  updatedAt        DateTime  @updatedAt @map("updated_at")
  deletedAt        DateTime? @map("deleted_at")

  // Relations
  school                School                 @relation(fields: [schoolId], references: [id])
  medium                Medium                 @relation(fields: [mediumId], references: [id])
  shift                 Shift?                 @relation(fields: [shiftId], references: [id])
  stream                Stream?                @relation(fields: [streamId], references: [id])
  electiveSubjectGroups ElectiveSubjectGroup[]
  classSubjects         ClassSubject[]
  classSections         ClassSection[]
  students              Student[]
  exams                 Exam[]
  fees                  Fee[]
  feesClassTypes        FeesClassType[]
  optionalFees          OptionalFee[]

  @@map("classes")
}

model ElectiveSubjectGroup {
  id                      Int       @id @default(autoincrement())
  totalSubjects           Int       @map("total_subjects")
  totalSelectableSubjects Int       @map("total_selectable_subjects")
  classId                 Int       @map("class_id")
  semesterId              Int?      @map("semester_id")
  schoolId                Int       @map("school_id")
  createdAt               DateTime  @default(now()) @map("created_at")
  updatedAt               DateTime  @updatedAt @map("updated_at")
  deletedAt               DateTime? @map("deleted_at")

  // Relations
  school        School         @relation(fields: [schoolId], references: [id])
  class         Class          @relation(fields: [classId], references: [id])
  semester      Semester?      @relation(fields: [semesterId], references: [id])
  classSubjects ClassSubject[]

  @@map("elective_subject_groups")
}

model ClassSubject {
  id                     Int       @id @default(autoincrement())
  classId                Int       @map("class_id")
  subjectId              Int       @map("subject_id")
  type                   String    @db.VarChar(32) // Compulsory / Elective
  electiveSubjectGroupId Int?      @map("elective_subject_group_id")
  semesterId             Int?      @map("semester_id")
  virtualSemesterId      Int       @map("virtual_semester_id") // Computed field
  schoolId               Int       @map("school_id")
  deletedAt              DateTime? @map("deleted_at")
  createdAt              DateTime  @default(now()) @map("created_at")
  updatedAt              DateTime  @updatedAt @map("updated_at")

  // Relations
  school               School                @relation(fields: [schoolId], references: [id])
  class                Class                 @relation(fields: [classId], references: [id])
  subject              Subject               @relation(fields: [subjectId], references: [id])
  electiveSubjectGroup ElectiveSubjectGroup? @relation(fields: [electiveSubjectGroupId], references: [id])
  semester             Semester?             @relation(fields: [semesterId], references: [id])
  studentSubjects      StudentSubject[]
  subjectTeachers      SubjectTeacher[]
  lessons              Lesson[]
  assignments          Assignment[]
  examTimetables       ExamTimetable[]
  examMarks            ExamMark[]
  onlineExams          OnlineExam[]
  onlineExamQuestions  OnlineExamQuestion[]
  announcementClasses  AnnouncementClass[]

  @@unique([classId, subjectId, virtualSemesterId], name: "unique_ids")
  @@map("class_subjects")
}

model Staff {
  id            Int       @id @default(autoincrement())
  userId        Int       @unique @map("user_id")
  qualification String?   @db.VarChar(512)
  salary        Float     @default(0)
  joiningDate   DateTime? @map("joining_date") @db.Date
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")

  // Relations
  user          User          @relation(fields: [userId], references: [id])
  expenses      Expense[]
  staffSalaries StaffSalary[]

  @@map("staffs")
}

model ClassSection {
  id        Int       @id @default(autoincrement())
  classId   Int       @map("class_id")
  sectionId Int       @map("section_id")
  mediumId  Int       @map("medium_id")
  schoolId  Int       @map("school_id")
  createdAt DateTime  @default(now()) @map("created_at")
  updatedAt DateTime  @updatedAt @map("updated_at")
  deletedAt DateTime? @map("deleted_at")

  // Relations
  school              School               @relation(fields: [schoolId], references: [id])
  class               Class                @relation(fields: [classId], references: [id])
  section             Section              @relation(fields: [sectionId], references: [id])
  medium              Medium               @relation(fields: [mediumId], references: [id])
  students            Student[]
  studentSubjects     StudentSubject[]
  subjectTeachers     SubjectTeacher[]
  lessons             Lesson[]
  assignments         Assignment[]
  examResults         ExamResult[]
  timetables          Timetable[]
  attendances         Attendance[]
  promoteStudents     PromoteStudent[]
  onlineExams         OnlineExam[]
  onlineExamQuestions OnlineExamQuestion[]
  classTeachers       ClassTeacher[]
  announcementClasses AnnouncementClass[]

  @@unique([classId, sectionId, mediumId], name: "unique_id")
  @@map("class_sections")
}

model Student {
  id                Int       @id @default(autoincrement())
  userId            Int       @map("user_id")
  classId           Int?      @map("class_id")
  classSectionId    Int?      @map("class_section_id")
  applicationType   String?   @default("offline") @map("application_type")
  admissionNo       String    @map("admission_no") @db.VarChar(512)
  rollNumber        Int?      @map("roll_number")
  admissionDate     DateTime  @map("admission_date") @db.Date
  schoolId          Int       @map("school_id")
  applicationStatus Int?      @default(1) @map("application_status") // 1- accepted, 0- rejected
  guardianId        Int       @map("guardian_id")
  sessionYearId     Int       @map("session_year_id")
  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @updatedAt @map("updated_at")
  deletedAt         DateTime? @map("deleted_at")

  // Relations
  school           School             @relation(fields: [schoolId], references: [id])
  user             User               @relation("StudentUser", fields: [userId], references: [id])
  guardian         User               @relation("GuardianUser", fields: [guardianId], references: [id])
  class            Class?             @relation(fields: [classId], references: [id])
  classSection     ClassSection?      @relation(fields: [classSectionId], references: [id])
  sessionYear      SessionYear        @relation(fields: [sessionYearId], references: [id])
  studentSubjects  StudentSubject[]
  promoteStudents  PromoteStudent[]
  extraStudentData ExtraStudentData[]

  @@map("students")
}

model StudentSubject {
  id             Int       @id @default(autoincrement())
  studentId      Int       @map("student_id") // user_id
  classSubjectId Int       @map("class_subject_id")
  classSectionId Int       @map("class_section_id")
  sessionYearId  Int       @map("session_year_id")
  schoolId       Int       @map("school_id")
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @updatedAt @map("updated_at")
  deletedAt      DateTime? @map("deleted_at")

  // Relations
  school       School       @relation(fields: [schoolId], references: [id])
  student      User         @relation(fields: [studentId], references: [id])
  classSubject ClassSubject @relation(fields: [classSubjectId], references: [id])
  classSection ClassSection @relation(fields: [classSectionId], references: [id])
  sessionYear  SessionYear  @relation(fields: [sessionYearId], references: [id])

  @@map("student_subjects")
}

model SubjectTeacher {
  id             Int       @id @default(autoincrement())
  classSectionId Int       @map("class_section_id")
  subjectId      Int       @map("subject_id")
  teacherId      Int       @map("teacher_id") // user_id
  classSubjectId Int       @map("class_subject_id")
  schoolId       Int       @map("school_id")
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @updatedAt @map("updated_at")
  deletedAt      DateTime? @map("deleted_at")

  // Relations
  school       School       @relation(fields: [schoolId], references: [id])
  classSection ClassSection @relation(fields: [classSectionId], references: [id])
  subject      Subject      @relation(fields: [subjectId], references: [id])
  teacher      User         @relation(fields: [teacherId], references: [id])
  classSubject ClassSubject @relation(fields: [classSubjectId], references: [id])
  timetables   Timetable[]

  @@unique([classSectionId, classSubjectId, teacherId], name: "unique_ids")
  @@map("subject_teachers")
}

model Lesson {
  id             Int      @id @default(autoincrement())
  name           String   @db.VarChar(512)
  description    String?  @db.VarChar(1024)
  classSectionId Int      @map("class_section_id")
  classSubjectId Int      @map("class_subject_id")
  schoolId       Int      @map("school_id")
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  // Relations
  school       School        @relation(fields: [schoolId], references: [id])
  classSection ClassSection  @relation(fields: [classSectionId], references: [id])
  classSubject ClassSubject  @relation(fields: [classSubjectId], references: [id])
  lessonTopics LessonTopic[]

  @@map("lessons")
}

model LessonTopic {
  id          Int      @id @default(autoincrement())
  lessonId    Int      @map("lesson_id")
  name        String   @db.VarChar(128)
  description String?  @db.VarChar(1024)
  schoolId    Int      @map("school_id")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  school School @relation(fields: [schoolId], references: [id])
  lesson Lesson @relation(fields: [lessonId], references: [id])

  @@map("lesson_topics")
}

model Assignment {
  id                       Int      @id @default(autoincrement())
  classSectionId           Int      @map("class_section_id")
  classSubjectId           Int      @map("class_subject_id")
  name                     String   @db.VarChar(128)
  instructions             String?  @db.VarChar(1024)
  dueDate                  DateTime @map("due_date")
  points                   Int?
  resubmission             Boolean  @default(false)
  extraDaysForResubmission Int?     @map("extra_days_for_resubmission")
  sessionYearId            Int      @map("session_year_id")
  schoolId                 Int      @map("school_id")
  createdBy                Int      @map("created_by") // teacher_user_id
  editedBy                 Int?     @map("edited_by") // teacher_user_id
  createdAt                DateTime @default(now()) @map("created_at")
  updatedAt                DateTime @updatedAt @map("updated_at")

  // Relations
  school                School                 @relation(fields: [schoolId], references: [id])
  classSection          ClassSection           @relation(fields: [classSectionId], references: [id])
  classSubject          ClassSubject           @relation(fields: [classSubjectId], references: [id])
  sessionYear           SessionYear            @relation(fields: [sessionYearId], references: [id])
  creator               User                   @relation("AssignmentCreatedBy", fields: [createdBy], references: [id])
  editor                User?                  @relation("AssignmentEditedBy", fields: [editedBy], references: [id])
  assignmentSubmissions AssignmentSubmission[]

  @@map("assignments")
}

model AssignmentSubmission {
  id            Int      @id @default(autoincrement())
  assignmentId  Int      @map("assignment_id")
  studentId     Int      @map("student_id")
  sessionYearId Int      @map("session_year_id")
  feedback      String?  @db.Text
  points        Int?
  status        Int      @default(0) // 0 = Pending/In Review , 1 = Accepted , 2 = Rejected , 3 = Resubmitted
  schoolId      Int      @map("school_id")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  // Relations
  school      School      @relation(fields: [schoolId], references: [id])
  assignment  Assignment  @relation(fields: [assignmentId], references: [id])
  student     User        @relation(fields: [studentId], references: [id])
  sessionYear SessionYear @relation(fields: [sessionYearId], references: [id])

  @@map("assignment_submissions")
}

model Exam {
  id            Int       @id @default(autoincrement())
  name          String    @db.VarChar(128)
  description   String?   @db.VarChar(1024)
  classId       Int       @map("class_id")
  sessionYearId Int       @map("session_year_id")
  startDate     DateTime? @map("start_date") @db.Date
  endDate       DateTime? @map("end_date") @db.Date
  publish       Int       @default(0)
  schoolId      Int       @map("school_id")
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  deletedAt     DateTime? @map("deleted_at")

  // Relations
  school         School          @relation(fields: [schoolId], references: [id])
  class          Class           @relation(fields: [classId], references: [id])
  sessionYear    SessionYear     @relation(fields: [sessionYearId], references: [id])
  examTimetables ExamTimetable[]
  examResults    ExamResult[]

  @@map("exams")
}

model ExamTimetable {
  id             Int      @id @default(autoincrement())
  examId         Int      @map("exam_id")
  classSubjectId Int      @map("class_subject_id")
  totalMarks     Float    @map("total_marks")
  passingMarks   Float    @map("passing_marks")
  date           DateTime @db.Date
  startTime      DateTime @map("start_time") @db.Time
  endTime        DateTime @map("end_time") @db.Time
  sessionYearId  Int      @map("session_year_id")
  schoolId       Int      @map("school_id")
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  // Relations
  school       School       @relation(fields: [schoolId], references: [id])
  exam         Exam         @relation(fields: [examId], references: [id])
  classSubject ClassSubject @relation(fields: [classSubjectId], references: [id])
  sessionYear  SessionYear  @relation(fields: [sessionYearId], references: [id])
  examMarks    ExamMark[]

  @@map("exam_timetables")
}

model ExamMark {
  id              Int       @id @default(autoincrement())
  examTimetableId Int       @map("exam_timetable_id")
  studentId       Int       @map("student_id") // user_id
  classSubjectId  Int       @map("class_subject_id")
  obtainedMarks   Float     @map("obtained_marks")
  teacherReview   String?   @map("teacher_review") @db.VarChar(1024)
  passingStatus   Boolean   @map("passing_status") // 1=Pass, 0=Fail
  sessionYearId   Int       @map("session_year_id")
  grade           String?   @db.TinyText
  schoolId        Int       @map("school_id")
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @updatedAt @map("updated_at")
  deletedAt       DateTime? @map("deleted_at")

  // Relations
  school        School        @relation(fields: [schoolId], references: [id])
  examTimetable ExamTimetable @relation(fields: [examTimetableId], references: [id])
  student       User          @relation(fields: [studentId], references: [id])
  classSubject  ClassSubject  @relation(fields: [classSubjectId], references: [id])
  sessionYear   SessionYear   @relation(fields: [sessionYearId], references: [id])

  @@map("exam_marks")
}

model ExamResult {
  id             Int      @id @default(autoincrement())
  examId         Int      @map("exam_id")
  classSectionId Int      @map("class_section_id")
  studentId      Int      @map("student_id") // user_id
  totalMarks     Int      @map("total_marks")
  obtainedMarks  Float    @map("obtained_marks")
  percentage     Float
  grade          String   @db.TinyText
  status         Int      @default(1) // 0 -> Failed, 1 -> Pass
  sessionYearId  Int      @map("session_year_id")
  schoolId       Int      @map("school_id")
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  // Relations
  school       School       @relation(fields: [schoolId], references: [id])
  exam         Exam         @relation(fields: [examId], references: [id])
  classSection ClassSection @relation(fields: [classSectionId], references: [id])
  student      User         @relation(fields: [studentId], references: [id])
  sessionYear  SessionYear  @relation(fields: [sessionYearId], references: [id])

  @@map("exam_results")
}

model Grade {
  id            Int      @id @default(autoincrement())
  startingRange Float    @map("starting_range")
  endingRange   Float    @map("ending_range")
  grade         String   @db.TinyText
  schoolId      Int      @map("school_id")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  // Relations
  school School @relation(fields: [schoolId], references: [id])

  @@map("grades")
}

model Timetable {
  id               Int           @id @default(autoincrement())
  subjectTeacherId Int?          @map("subject_teacher_id")
  classSectionId   Int           @map("class_section_id")
  subjectId        Int?          @map("subject_id")
  startTime        DateTime      @map("start_time") @db.Time
  endTime          DateTime      @map("end_time") @db.Time
  note             String?       @db.VarChar(1024)
  day              TimetableDay
  type             TimetableType
  semesterId       Int?          @map("semester_id")
  schoolId         Int           @map("school_id")
  createdAt        DateTime      @default(now()) @map("created_at")
  updatedAt        DateTime      @updatedAt @map("updated_at")

  // Relations
  school         School          @relation(fields: [schoolId], references: [id])
  subjectTeacher SubjectTeacher? @relation(fields: [subjectTeacherId], references: [id])
  classSection   ClassSection    @relation(fields: [classSectionId], references: [id])
  subject        Subject?        @relation(fields: [subjectId], references: [id])
  semester       Semester?       @relation(fields: [semesterId], references: [id])

  @@map("timetables")
}

enum TimetableDay {
  Monday
  Tuesday
  Wednesday
  Thursday
  Friday
  Saturday
  Sunday
}

enum TimetableType {
  Lecture
  Break
}

model Announcement {
  id            Int      @id @default(autoincrement())
  title         String   @db.VarChar(128)
  description   String?  @db.LongText
  sessionYearId Int      @map("session_year_id")
  schoolId      Int      @map("school_id")
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  // Relations
  school              School              @relation(fields: [schoolId], references: [id])
  sessionYear         SessionYear         @relation(fields: [sessionYearId], references: [id])
  announcementClasses AnnouncementClass[]

  @@map("announcements")
}

model AcademicCalendar {
  id            Int       @id @default(autoincrement())
  date          DateTime  @db.Date
  title         String    @db.VarChar(512)
  description   String?   @db.VarChar(1024)
  sessionYearId Int       @map("session_year_id")
  schoolId      Int       @map("school_id")
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")
  deletedAt     DateTime? @map("deleted_at")

  // Relations
  school      School      @relation(fields: [schoolId], references: [id])
  sessionYear SessionYear @relation(fields: [sessionYearId], references: [id])

  @@map("academic_calendars")
}
